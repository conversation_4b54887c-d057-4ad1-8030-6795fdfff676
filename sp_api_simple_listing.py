#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon SP-API 简化版商品发布脚本
使用Amazon Selling Partner API创建和管理商品listing

这是一个简化版本，专注于核心功能演示
实际生产环境建议使用官方SDK或更完整的实现

作者: AI Assistant
版本: 1.0
"""

import json
import time
import requests
from datetime import datetime, timedelta


class SimpleSPAPIClient:
    """简化版Amazon SP-API客户端"""
    
    def __init__(self, config):
        """
        初始化客户端
        
        Args:
            config (dict): 配置信息
        """
        self.config = config
        self.access_token = None
        self.token_expires_at = None
        self.lwa_endpoint = 'https://api.amazon.com/auth/o2/token'
        
    def get_access_token(self):
        """获取LWA访问令牌"""
        if self.access_token and self.token_expires_at and datetime.now() < self.token_expires_at:
            return self.access_token
            
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        data = {
            'grant_type': 'refresh_token',
            'refresh_token': self.config['refresh_token'],
            'client_id': self.config['client_id'],
            'client_secret': self.config['client_secret']
        }
        
        try:
            response = requests.post(self.lwa_endpoint, headers=headers, data=data, timeout=30)
            
            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data['access_token']
                expires_in = token_data.get('expires_in', 3600)
                self.token_expires_at = datetime.now() + timedelta(seconds=expires_in - 60)
                return self.access_token
            else:
                raise Exception(f"获取访问令牌失败: {response.status_code} - {response.text}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")
    
    def make_api_request(self, method, path, params=None, data=None):
        """发送API请求"""
        access_token = self.get_access_token()
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json',
            'x-amz-access-token': access_token,
            'User-Agent': 'SP-API-Simple-Client/1.0 (Language=Python)'
        }
        
        url = f"{self.config['endpoint']}{path}"
        
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                json=data if data else None,
                timeout=30
            )
            return response
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"API请求失败: {str(e)}")
    
    def create_listing(self, sku, listing_data):
        """创建商品listing"""
        path = f"/listings/2021-08-01/items/{self.config['seller_id']}/{sku}"
        params = {
            'marketplaceIds': self.config['marketplace_id']
        }
        
        return self.make_api_request('PUT', path, params, listing_data)
    
    def get_listing(self, sku, include_data=None):
        """获取商品listing"""
        path = f"/listings/2021-08-01/items/{self.config['seller_id']}/{sku}"
        params = {
            'marketplaceIds': self.config['marketplace_id']
        }
        
        if include_data:
            params['includedData'] = ','.join(include_data)
        
        return self.make_api_request('GET', path, params)
    
    def update_listing_price(self, sku, new_price):
        """更新商品价格"""
        path = f"/listings/2021-08-01/items/{self.config['seller_id']}/{sku}"
        params = {
            'marketplaceIds': self.config['marketplace_id']
        }
        
        patch_data = {
            "patches": [
                {
                    "op": "replace",
                    "path": "/attributes/purchasable_offer",
                    "value": [
                        {
                            "currency": "USD",
                            "our_price": [
                                {
                                    "schedule": [
                                        {
                                            "value_with_tax": new_price
                                        }
                                    ]
                                }
                            ],
                            "audience": "ALL"
                        }
                    ]
                }
            ]
        }
        
        return self.make_api_request('PATCH', path, params, patch_data)
    
    def update_listing_quantity(self, sku, new_quantity):
        """更新商品库存数量"""
        path = f"/listings/2021-08-01/items/{self.config['seller_id']}/{sku}"
        params = {
            'marketplaceIds': self.config['marketplace_id']
        }
        
        patch_data = {
            "patches": [
                {
                    "op": "replace",
                    "path": "/attributes/fulfillment_availability",
                    "value": [
                        {
                            "fulfillment_channel_code": "DEFAULT",
                            "quantity": new_quantity
                        }
                    ]
                }
            ]
        }
        
        return self.make_api_request('PATCH', path, params, patch_data)


def create_test_product_data():
    """创建测试商品数据"""
    return {
        "productType": "PRODUCT",
        "attributes": {
            "item_name": [
                {
                    "value": "测试商品 - Python SP-API Demo",
                    "language_tag": "en_US"
                }
            ],
            "brand": [
                {
                    "value": "Test Brand"
                }
            ],
            "description": [
                {
                    "value": "This is a test product created via Amazon SP-API using Python",
                    "language_tag": "en_US"
                }
            ],
            "purchasable_offer": [
                {
                    "currency": "USD",
                    "our_price": [
                        {
                            "schedule": [
                                {
                                    "value_with_tax": 19.99
                                }
                            ]
                        }
                    ],
                    "audience": "ALL"
                }
            ],
            "fulfillment_availability": [
                {
                    "fulfillment_channel_code": "DEFAULT",
                    "quantity": 50
                }
            ]
        }
    }


def load_config(config_file='config.json'):
    """加载配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            return config_data.get('config', config_data)
    except FileNotFoundError:
        print(f"❌ 配置文件 {config_file} 不存在")
        print("请复制 config_template.json 为 config.json 并填入您的凭证信息")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        return None


def validate_config(config):
    """验证配置"""
    required_fields = ['client_id', 'client_secret', 'refresh_token', 'seller_id', 'marketplace_id', 'endpoint']
    missing_fields = []
    
    for field in required_fields:
        if not config.get(field) or config[field].startswith('YOUR_'):
            missing_fields.append(field)
    
    return missing_fields


def main():
    """主函数"""
    print("🚀 Amazon SP-API 商品发布测试")
    print("=" * 50)
    
    # 加载配置
    config = load_config()
    if not config:
        return
    
    # 验证配置
    missing_fields = validate_config(config)
    if missing_fields:
        print("❌ 配置不完整，缺少以下字段:")
        for field in missing_fields:
            print(f"   - {field}")
        print("\n请在 config.json 中填入正确的值")
        return
    
    # 初始化客户端
    client = SimpleSPAPIClient(config)
    
    # 生成测试SKU
    test_sku = f"TEST-{int(time.time())}"
    
    print(f"📦 测试SKU: {test_sku}")
    print(f"🌍 市场: {config['marketplace_id']}")
    print(f"🏪 卖家: {config['seller_id']}")
    print("-" * 50)
    
    try:
        # 步骤1: 获取访问令牌
        print("1️⃣ 获取访问令牌...")
        token = client.get_access_token()
        print(f"✅ 令牌获取成功: {token[:20]}...")
        
        # 步骤2: 创建商品
        print(f"\n2️⃣ 创建商品 (SKU: {test_sku})...")
        product_data = create_test_product_data()
        response = client.create_listing(test_sku, product_data)
        
        if response.status_code in [200, 202]:
            print("✅ 商品创建请求已提交")
            print(f"   状态码: {response.status_code}")
            if response.text:
                result = response.json() if response.text.strip() else {}
                if result:
                    print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 商品创建失败")
            print(f"   状态码: {response.status_code}")
            print(f"   错误: {response.text}")
            return
        
        # 等待处理
        print("\n⏳ 等待Amazon处理请求...")
        time.sleep(3)
        
        # 步骤3: 查询商品信息
        print(f"\n3️⃣ 查询商品信息...")
        response = client.get_listing(test_sku, ['summaries', 'attributes', 'issues'])
        
        if response.status_code == 200:
            print("✅ 商品信息获取成功")
            listing_info = response.json()
            print(f"   商品详情: {json.dumps(listing_info, indent=2, ensure_ascii=False)}")
        else:
            print(f"⚠️ 商品信息获取失败: {response.status_code}")
            if response.text:
                print(f"   详情: {response.text}")
        
        # 步骤4: 更新价格
        print(f"\n4️⃣ 更新商品价格...")
        new_price = 29.99
        response = client.update_listing_price(test_sku, new_price)
        
        if response.status_code in [200, 202]:
            print(f"✅ 价格更新成功 (新价格: ${new_price})")
        else:
            print(f"❌ 价格更新失败: {response.status_code}")
            if response.text:
                print(f"   错误: {response.text}")
        
        # 步骤5: 更新库存
        print(f"\n5️⃣ 更新商品库存...")
        new_quantity = 100
        response = client.update_listing_quantity(test_sku, new_quantity)
        
        if response.status_code in [200, 202]:
            print(f"✅ 库存更新成功 (新库存: {new_quantity})")
        else:
            print(f"❌ 库存更新失败: {response.status_code}")
            if response.text:
                print(f"   错误: {response.text}")
        
        print(f"\n🎉 测试完成!")
        print(f"📦 测试SKU: {test_sku}")
        print("💡 提示: 这是测试商品，请根据需要在卖家中心管理")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
