# Amazon SP-API 商品发布测试脚本

这个项目包含了使用Amazon Selling Partner API (SP-API) 创建和管理商品listing的Python脚本。

## 📋 功能特性

- ✅ 获取LWA访问令牌
- ✅ 创建商品listing
- ✅ 查询商品信息
- ✅ 更新商品价格
- ✅ 更新商品库存
- ✅ 错误处理和日志记录

## 📁 文件说明

- `sp_api_simple_listing.py` - 简化版SP-API客户端和测试脚本
- `amazon_sp_api_listing_test.py` - 完整版SP-API客户端（包含AWS签名）
- `config_template.json` - 配置文件模板
- `requirements.txt` - Python依赖包列表
- `README.md` - 使用说明文档

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置SP-API凭证

1. 复制配置模板：
```bash
cp config_template.json config.json
```

2. 编辑 `config.json`，填入您的SP-API凭证：
```json
{
  "config": {
    "client_id": "您的LWA客户端ID",
    "client_secret": "您的LWA客户端密钥",
    "refresh_token": "您的刷新令牌",
    "seller_id": "您的卖家ID",
    "marketplace_id": "ATVPDKIKX0DER",
    "region": "us-east-1",
    "endpoint": "https://sellingpartnerapi-na.amazon.com"
  }
}
```

### 3. 运行测试脚本

```bash
python sp_api_simple_listing.py
```

## 🔧 配置说明

### 必需配置项

| 配置项 | 说明 | 示例 |
|--------|------|------|
| `client_id` | LWA应用程序客户端ID | `amzn1.application-oa2-client.xxx` |
| `client_secret` | LWA应用程序客户端密钥 | `xxx` |
| `refresh_token` | 授权刷新令牌 | `Atzr|xxx` |
| `seller_id` | 卖家ID | `A1XXXXXXXXXXXX` |
| `marketplace_id` | 市场ID | `ATVPDKIKX0DER` (美国) |
| `endpoint` | API端点 | `https://sellingpartnerapi-na.amazon.com` |

### 主要市场ID

| 市场 | Marketplace ID |
|------|----------------|
| 美国 | ATVPDKIKX0DER |
| 加拿大 | A2EUQ1WTGCTBG2 |
| 墨西哥 | A1AM78C64UM0Y8 |
| 英国 | A1F83G8C2ARO7P |
| 德国 | A1PA6795UKMFR9 |
| 法国 | A13V1IB3VIYZZH |
| 意大利 | APJ6JRA9NG5V4 |
| 西班牙 | A1RKKUPIHCS9HS |
| 日本 | A1VC38T7YXB528 |
| 澳大利亚 | A39IBJ37TRP1C6 |

### API端点

| 区域 | 端点 |
|------|------|
| 北美 | https://sellingpartnerapi-na.amazon.com |
| 欧洲 | https://sellingpartnerapi-eu.amazon.com |
| 远东 | https://sellingpartnerapi-fe.amazon.com |

## 📖 使用示例

### 创建商品listing

```python
from sp_api_simple_listing import SimpleSPAPIClient, create_test_product_data

# 初始化客户端
client = SimpleSPAPIClient(config)

# 创建商品数据
product_data = create_test_product_data()

# 创建listing
response = client.create_listing("MY-SKU-001", product_data)
```

### 更新商品价格

```python
# 更新价格为$39.99
response = client.update_listing_price("MY-SKU-001", 39.99)
```

### 更新商品库存

```python
# 更新库存为200件
response = client.update_listing_quantity("MY-SKU-001", 200)
```

### 查询商品信息

```python
# 获取商品详细信息
response = client.get_listing("MY-SKU-001", ['summaries', 'attributes', 'issues'])
```

## ⚠️ 注意事项

### 1. 权限要求
- 需要在Amazon开发者控制台注册SP-API应用程序
- 需要获得卖家的授权（refresh_token）
- 需要申请相应的API权限角色

### 2. 限制说明
- SP-API有速率限制，请注意控制请求频率
- 某些产品类型可能需要特殊权限
- 测试环境和生产环境使用不同的端点

### 3. 安全建议
- 不要将包含真实凭证的配置文件提交到版本控制
- 定期轮换访问密钥
- 使用HTTPS进行所有API调用

## 🐛 常见问题

### Q: 获取访问令牌失败
A: 检查client_id、client_secret和refresh_token是否正确

### Q: 创建listing失败
A: 
- 检查seller_id和marketplace_id是否匹配
- 确认产品数据格式符合Amazon要求
- 查看错误响应中的具体错误信息

### Q: 403 Forbidden错误
A: 
- 检查应用程序是否有相应的API权限
- 确认卖家是否已授权您的应用程序

### Q: 商品不显示在Amazon上
A: 
- 商品创建后需要时间处理
- 检查商品是否有validation错误
- 确认商品类型和属性是否正确

## 📚 相关资源

- [Amazon SP-API官方文档](https://developer-docs.amazon.com/sp-api/)
- [Listings Items API参考](https://developer-docs.amazon.com/sp-api/docs/listings-items-api-v2021-08-01-use-case-guide)
- [Product Type Definitions API](https://developer-docs.amazon.com/sp-api/docs/product-type-api-use-case-guide)
- [Amazon开发者控制台](https://developer.amazonservices.com/)

## 📄 许可证

本项目仅供学习和测试使用，请遵守Amazon SP-API的使用条款和条件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**免责声明**: 这是一个演示项目，实际生产环境中请使用官方SDK或经过充分测试的解决方案。
