#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon SP-API 商品发布测试脚本
使用Amazon Selling Partner API创建和管理商品listing

依赖包安装:
pip install requests boto3 python-jose cryptography

使用前需要配置:
1. Amazon SP-API应用程序凭证
2. 刷新令牌 (Refresh Token)
3. 市场ID (Marketplace ID)
4. 卖家ID (Seller ID)
"""

import json
import time
import hashlib
import hmac
import base64
import urllib.parse
from datetime import datetime, timezone, timedelta
import requests
from jose import jwt
import boto3
from botocore.auth import SigV4Auth
from botocore.awsrequest import AWSRequest


class AmazonSPAPIClient:
    """Amazon SP-API客户端"""

    def __init__(self, config):
        """
        初始化SP-API客户端

        Args:
            config (dict): 配置信息，包含以下字段:
                - client_id: LWA客户端ID
                - client_secret: LWA客户端密钥
                - refresh_token: 刷新令牌
                - seller_id: 卖家ID
                - marketplace_id: 市场ID
                - region: AWS区域 (默认: us-east-1)
                - endpoint: API端点 (默认: https://sellingpartnerapi-na.amazon.com)
        """
        self.config = config
        self.access_token = None
        self.token_expires_at = None

        # 默认配置
        self.region = config.get('region', 'us-east-1')
        self.endpoint = config.get('endpoint', 'https://sellingpartnerapi-na.amazon.com')
        self.lwa_endpoint = 'https://api.amazon.com/auth/o2/token'

    def get_access_token(self):
        """获取访问令牌"""
        if self.access_token and self.token_expires_at and datetime.now() < self.token_expires_at:
            return self.access_token

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        data = {
            'grant_type': 'refresh_token',
            'refresh_token': self.config['refresh_token'],
            'client_id': self.config['client_id'],
            'client_secret': self.config['client_secret']
        }

        response = requests.post(self.lwa_endpoint, headers=headers, data=data)

        if response.status_code == 200:
            token_data = response.json()
            self.access_token = token_data['access_token']
            expires_in = token_data.get('expires_in', 3600)
            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in - 60)
            return self.access_token
        else:
            raise Exception(f"获取访问令牌失败: {response.status_code} - {response.text}")

    def sign_request(self, method, url, headers, payload=''):
        """AWS签名v4"""
        # 这里简化处理，实际使用中建议使用boto3的签名功能
        # 或者使用专门的SP-API SDK
        pass

    def make_request(self, method, path, params=None, data=None):
        """发送API请求"""
        access_token = self.get_access_token()

        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json',
            'x-amz-access-token': access_token,
            'User-Agent': 'SP-API-Test-Client/1.0'
        }

        url = f"{self.endpoint}{path}"

        if params:
            url += '?' + urllib.parse.urlencode(params)

        response = requests.request(
            method=method,
            url=url,
            headers=headers,
            json=data if data else None
        )

        return response

    def get_product_type_definitions(self, product_type):
        """获取产品类型定义"""
        path = f"/definitions/2020-09-01/productTypes/{product_type}"
        params = {
            'sellerId': self.config['seller_id'],
            'marketplaceIds': self.config['marketplace_id']
        }

        response = self.make_request('GET', path, params)
        return response

    def create_listing(self, sku, listing_data):
        """创建商品listing"""
        path = f"/listings/2021-08-01/items/{self.config['seller_id']}/{sku}"
        params = {
            'marketplaceIds': self.config['marketplace_id']
        }

        response = self.make_request('PUT', path, params, listing_data)
        return response

    def update_listing(self, sku, patch_data):
        """更新商品listing"""
        path = f"/listings/2021-08-01/items/{self.config['seller_id']}/{sku}"
        params = {
            'marketplaceIds': self.config['marketplace_id']
        }

        response = self.make_request('PATCH', path, params, patch_data)
        return response

    def get_listing(self, sku, include_data=None):
        """获取商品listing详情"""
        path = f"/listings/2021-08-01/items/{self.config['seller_id']}/{sku}"
        params = {
            'marketplaceIds': self.config['marketplace_id']
        }

        if include_data:
            params['includedData'] = ','.join(include_data)

        response = self.make_request('GET', path, params)
        return response

    def delete_listing(self, sku):
        """删除商品listing"""
        path = f"/listings/2021-08-01/items/{self.config['seller_id']}/{sku}"
        params = {
            'marketplaceIds': self.config['marketplace_id']
        }

        response = self.make_request('DELETE', path, params)
        return response


def create_sample_listing_data():
    """创建示例商品数据"""
    return {
        "productType": "PRODUCT",
        "attributes": {
            "item_name": [
                {
                    "value": "测试商品 - Python SP-API",
                    "language_tag": "zh_CN"
                }
            ],
            "brand": [
                {
                    "value": "测试品牌"
                }
            ],
            "description": [
                {
                    "value": "这是一个通过Amazon SP-API创建的测试商品",
                    "language_tag": "zh_CN"
                }
            ],
            "purchasable_offer": [
                {
                    "currency": "USD",
                    "our_price": [
                        {
                            "schedule": [
                                {
                                    "value_with_tax": 29.99
                                }
                            ]
                        }
                    ],
                    "audience": "ALL"
                }
            ],
            "fulfillment_availability": [
                {
                    "fulfillment_channel_code": "DEFAULT",
                    "quantity": 100
                }
            ]
        }
    }


def create_price_update_patch():
    """创建价格更新补丁"""
    return {
        "patches": [
            {
                "op": "replace",
                "path": "/attributes/purchasable_offer",
                "value": [
                    {
                        "currency": "USD",
                        "our_price": [
                            {
                                "schedule": [
                                    {
                                        "value_with_tax": 39.99
                                    }
                                ]
                            }
                        ],
                        "audience": "ALL"
                    }
                ]
            }
        ]
    }


def main():
    """主函数 - 演示SP-API商品发布流程"""

    # 配置信息 - 请替换为您的实际配置
    config = {
        'client_id': 'YOUR_LWA_CLIENT_ID',
        'client_secret': 'YOUR_LWA_CLIENT_SECRET',
        'refresh_token': 'YOUR_REFRESH_TOKEN',
        'seller_id': 'YOUR_SELLER_ID',
        'marketplace_id': 'ATVPDKIKX0DER',  # 美国市场
        'region': 'us-east-1',
        'endpoint': 'https://sellingpartnerapi-na.amazon.com'
    }

    # 检查配置
    required_fields = ['client_id', 'client_secret', 'refresh_token', 'seller_id']
    missing_fields = [field for field in required_fields if config[field].startswith('YOUR_')]

    if missing_fields:
        print("❌ 错误: 请先配置以下字段:")
        for field in missing_fields:
            print(f"   - {field}")
        print("\n请在脚本中替换相应的配置值后再运行。")
        return

    # 初始化客户端
    client = AmazonSPAPIClient(config)

    # 测试SKU
    test_sku = f"TEST-SKU-{int(time.time())}"

    print("🚀 Amazon SP-API 商品发布测试开始")
    print(f"测试SKU: {test_sku}")
    print("-" * 50)

    try:
        # 1. 获取访问令牌
        print("1️⃣ 获取访问令牌...")
        token = client.get_access_token()
        print(f"✅ 访问令牌获取成功: {token[:20]}...")

        # 2. 创建商品listing
        print("\n2️⃣ 创建商品listing...")
        listing_data = create_sample_listing_data()
        response = client.create_listing(test_sku, listing_data)

        if response.status_code in [200, 202]:
            print("✅ 商品listing创建成功")
            print(f"响应状态码: {response.status_code}")
            if response.text:
                print(f"响应内容: {response.text}")
        else:
            print(f"❌ 商品listing创建失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return

        # 等待处理
        print("\n⏳ 等待5秒让Amazon处理请求...")
        time.sleep(5)

        # 3. 获取商品详情
        print("\n3️⃣ 获取商品listing详情...")
        response = client.get_listing(test_sku, ['summaries', 'attributes', 'issues'])

        if response.status_code == 200:
            print("✅ 商品详情获取成功")
            listing_info = response.json()
            print(f"商品信息: {json.dumps(listing_info, indent=2, ensure_ascii=False)}")
        else:
            print(f"⚠️ 商品详情获取失败: {response.status_code}")
            print(f"信息: {response.text}")

        # 4. 更新价格
        print("\n4️⃣ 更新商品价格...")
        patch_data = create_price_update_patch()
        response = client.update_listing(test_sku, patch_data)

        if response.status_code in [200, 202]:
            print("✅ 价格更新成功")
        else:
            print(f"❌ 价格更新失败: {response.status_code}")
            print(f"错误信息: {response.text}")

        print("\n🎉 测试完成!")
        print(f"测试SKU: {test_sku}")
        print("注意: 这是一个测试商品，请根据需要删除或修改。")

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
